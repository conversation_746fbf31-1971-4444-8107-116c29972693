import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RedisCacheProvider } from '@repo/nestjs-commons/cache';
import {
    ApiKey,
    ApiKeyRepository,
    App,
    AppInstallation,
    AppInstallationRepository,
    AppRepository,
    CachedApiKeyRepository,
    CachedAppInstallationsRepository,
    CachedOrganizationRepository,
    CachedUserRepository,
    OAuthAuthorizationCode,
    OAuthClient,
    OAuthToken,
    Organization,
    OrganizationRepository,
    TransactionService,
    User,
    UserRepository,
} from '@repo/thena-platform-entities';
import { CommonModule } from '../common/common.module';
import { SecureStorageModule } from '../secure-storage/secure-storage.module';
import { ApiKeyController } from './controllers/api-key.controller';
import { AuthenticationController } from './controllers/authentication.controller';
import { AuthenticationGrpcController } from './controllers/authentication.grpc.controller';
import { OAuthController } from './controllers/oauth.controller';
import { GrpcAuthGuard } from './grpc-auth.guard';
import { OAuthAuthGuard } from './oauth-auth.guard';
import { ApiKeyService } from './services/api-key.service';
import { AuthenticationService } from './services/authentication.service';
import { OAuthService } from './services/oauth.service';
import { SupabaseAuthGuard } from './supabase-auth.guard';

@Module({
  imports: [
    CommonModule,
    SecureStorageModule,

    // Register TypeOrm module
    TypeOrmModule.forFeature([
      // Platform users
      User,
      UserRepository,

      // Platform organizations
      Organization,
      OrganizationRepository,

      // API keys
      ApiKey,
      ApiKeyRepository,

      // App
      App,
      AppRepository,

      // App Installation
      AppInstallation,
      AppInstallationRepository,

      // OAuth
      OAuthClient,
      OAuthAuthorizationCode,
      OAuthToken,
    ]),
  ],
  providers: [
    // Redis cache provider
    RedisCacheProvider,

    // Grpc auth guard
    GrpcAuthGuard,
    // Supabase auth guard
    SupabaseAuthGuard,

    // OAuth auth guard
    OAuthAuthGuard,

    // Transaction service
    TransactionService,

    // User
    CachedUserRepository,

    // Organization
    CachedOrganizationRepository,

    // API Key
    CachedApiKeyRepository,

    // App Installation
    CachedAppInstallationsRepository,

    // Module's own services
    AuthenticationService,
    ApiKeyService,

    // OAuth
    OAuthService,
  ],
  controllers: [
    AuthenticationController,
    AuthenticationGrpcController,
    ApiKeyController,
    OAuthController,
  ],
})
export class AuthenticationModule {}
